#!/usr/bin/env python3
"""
简单的并行测试：4客户端，2维，20次评估
"""

import numpy as np
import time
from pyiaf.parallel_iaf_fbo import ParallelIAF_FBO
from pyiaf.Tasks.benchmark import create_tasks_diff_func

def simple_parallel_test():
    """运行简单的并行测试"""
    
    # 测试参数
    n_clients = 4
    dimension = 2
    n_initial = 10
    max_iterations = 10
    
    print(f"开始简单并行测试:")
    print(f"- 客户端数量: {n_clients}")
    print(f"- 问题维度: {dimension}")
    print(f"- 初始采样: {n_initial}")
    print(f"- 最大迭代: {max_iterations}")
    print("-" * 30)
    
    # 创建任务函数
    tasks = create_tasks_diff_func(dim=dimension, normalized=False)[:n_clients]
    print(f"创建了 {len(tasks)} 个基准任务")
    
    # 设置边界
    bounds = (np.array([-5.0, -5.0]), np.array([5.0, 5.0]))
    
    # 创建并行优化器
    optimizer = ParallelIAF_FBO(
        n_clients=n_clients,
        bounds=bounds,
        n_initial=n_initial,
        max_iterations=max_iterations,
        af_type='LCB',
        n_clusters=2,
        pop_size=20,
        cso_iters=20,
        transfer_prob=0.5,
        noise_prob=0.0,
        random_state=42,
        n_processes=2  # 使用2个进程
    )
    
    # 设置目标函数
    objective_functions = {}
    for client_id in range(n_clients):
        task = tasks[client_id]
        objective_functions[client_id] = lambda x, t=task: t(x)
    
    print("开始优化...")
    start_time = time.time()
    
    optimizer.setup_clients(objective_functions)
    results = optimizer.run_optimization()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"优化完成，总耗时: {total_time:.2f}秒")
    print("-" * 30)
    
    # 输出结果
    print("优化结果:")
    for client_id in range(n_clients):
        best_val = results['best_values'][client_id]
        print(f"客户端 {client_id}: 最优值 = {best_val:.6f}")
    
    return results, total_time

if __name__ == "__main__":
    try:
        results, runtime = simple_parallel_test()
        print(f"\n简单并行测试成功完成！总运行时间: {runtime:.2f}秒")
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
