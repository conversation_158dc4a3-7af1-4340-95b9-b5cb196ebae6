function [ T ] = Schwefel_( X , M,opt )
%ROSENBROCK20 Summary of this function goes here
% %%%%%Shifte<PERSON><PERSON>’s Function [-100 100] F6
T = [];

for t = 1:size(X,1)
    x = X(t,:);
    
    var = x;
    dim = length(var);
    var = (M*(var-opt)')';
    sum = 0;
    for i = 1: dim
        sum = sum + var(i)*sin(sqrt(abs(var(i))));
    end
    
    obj = 418.9829*dim-sum;

    T(t,:) = obj;
end
end

